<?php
/**
 * Performance Configuration for Large File Processing
 * CDCO CSV Import System - High Performance Settings
 */

// Remove all time limits
ini_set('max_execution_time', 0);
set_time_limit(0);
ini_set('max_input_time', 0);

// Increase memory limits
ini_set('memory_limit', '2048M'); // 2GB for very large files
ini_set('post_max_size', '1024M'); // 1GB post size
ini_set('upload_max_filesize', '1024M'); // 1GB file size

// Database and connection settings
ini_set('mysql.connect_timeout', 600); // 10 minutes
ini_set('default_socket_timeout', 600); // 10 minutes

// Input settings for large forms
ini_set('max_input_vars', 50000); // Increase input variables
ini_set('max_input_nesting_level', 64); // Increase nesting level

// Output buffering settings
ini_set('output_buffering', 'Off');
ini_set('implicit_flush', 'On');

// Error reporting (disable for production)
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

// Session settings for long operations
ini_set('session.gc_maxlifetime', 7200); // 2 hours
ini_set('session.cookie_lifetime', 7200); // 2 hours

/**
 * Function to check and display current PHP limits
 */
function displayCurrentLimits() {
    $limits = [
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_limit' => ini_get('memory_limit'),
        'post_max_size' => ini_get('post_max_size'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'max_input_time' => ini_get('max_input_time'),
        'max_input_vars' => ini_get('max_input_vars'),
        'default_socket_timeout' => ini_get('default_socket_timeout')
    ];
    
    return $limits;
}

/**
 * Function to optimize PHP settings for large file processing
 */
function optimizeForLargeFiles() {
    // Disable output buffering for real-time progress
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Flush output immediately
    if (function_exists('fastcgi_finish_request')) {
        fastcgi_finish_request();
    }
    
    // Garbage collection optimization
    if (function_exists('gc_enable')) {
        gc_enable();
        gc_collect_cycles();
    }
    
    // Set higher memory limit if needed
    $current_memory = ini_get('memory_limit');
    if (parseMemoryLimit($current_memory) < parseMemoryLimit('1024M')) {
        ini_set('memory_limit', '2048M');
    }
}

/**
 * Convert memory limit string to bytes
 */
function parseMemoryLimit($limit) {
    if ($limit == -1) return PHP_INT_MAX;
    
    $limit = trim($limit);
    $last = strtolower($limit[strlen($limit)-1]);
    $limit = (int) $limit;
    
    switch($last) {
        case 'g': $limit *= 1024;
        case 'm': $limit *= 1024;
        case 'k': $limit *= 1024;
    }
    
    return $limit;
}

/**
 * Monitor memory usage during processing
 */
function getMemoryUsage() {
    return [
        'current' => memory_get_usage(true),
        'peak' => memory_get_peak_usage(true),
        'current_formatted' => formatBytes(memory_get_usage(true)),
        'peak_formatted' => formatBytes(memory_get_peak_usage(true)),
        'limit' => ini_get('memory_limit')
    ];
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Check if we're running in CLI mode
 */
function isCLI() {
    return php_sapi_name() === 'cli';
}

/**
 * Set optimal settings based on environment
 */
function setOptimalSettings() {
    if (isCLI()) {
        // CLI mode - no limits
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', -1);
    } else {
        // Web mode - high but reasonable limits
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '2048M');
    }
    
    // Common optimizations
    optimizeForLargeFiles();
}

// Apply optimal settings when this file is included
setOptimalSettings();

// Log current settings
error_log("Performance Config Loaded - Memory: " . ini_get('memory_limit') . 
          ", Execution Time: " . ini_get('max_execution_time') . 
          ", Upload Size: " . ini_get('upload_max_filesize'));
?>
