<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام استيراد ملفات CSV - CDCO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 40px;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background: #e8f4fd;
            border-color: #0056b3;
        }
        
        .upload-area.dragover {
            background: #e8f4fd;
            border-color: #0056b3;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 3em;
            color: #007cba;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .progress-container {
            display: none;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .setup-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .setup-link a {
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }
        
        .setup-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ نظام استيراد ملفات CSV</h1>
            <p>استيراد ودمج ملفات CSV في قاعدة البيانات - CDCO</p>
        </div>
        
        <div class="main-card">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="upload-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📁</div>
                        <h3>اسحب وأفلت ملفات CSV هنا</h3>
                        <p>أو انقر لاختيار الملفات</p>
                        <input type="file" id="fileInput" name="csv_files[]" class="file-input" multiple accept=".csv">
                        <button type="button" class="btn" onclick="document.getElementById('fileInput').click()">
                            اختيار الملفات
                        </button>
                    </div>
                </div>
                
                <div class="file-info" id="fileInfo">
                    <h4>الملفات المحددة:</h4>
                    <div id="fileList"></div>
                </div>
                
                <div class="progress-container" id="progressContainer">
                    <h4>جاري الاستيراد...</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">0%</p>
                </div>
                
                <div class="alert alert-success" id="successAlert"></div>
                <div class="alert alert-error" id="errorAlert"></div>
                
                <div style="text-align: center;">
                    <button type="submit" class="btn" id="uploadBtn" disabled>
                        🚀 بدء الاستيراد
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="location.reload()">
                        🔄 إعادة تعيين
                    </button>
                    <a href="view_data.php" class="btn" style="background: #28a745;">
                        📊 عرض البيانات
                    </a>
                </div>
            </form>
        </div>
        
        <div class="main-card">
            <h3 style="text-align: center; margin-bottom: 20px;">📊 إحصائيات قاعدة البيانات</h3>
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="stat-number" id="totalRecords">-</div>
                    <div>إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalFiles">-</div>
                    <div>الملفات المستوردة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastImport">-</div>
                    <div>آخر استيراد</div>
                </div>
            </div>
        </div>
        
        <div class="setup-link">
            <p>هل تحتاج لإعداد قاعدة البيانات؟ <a href="database_setup.php">انقر هنا لإعداد قاعدة البيانات</a></p>
        </div>
    </div>

    <script>
        // Load statistics on page load
        loadStats();

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileList = document.getElementById('fileList');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            fileList.innerHTML = '';
            for (let file of files) {
                if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
                    const fileItem = document.createElement('div');
                    fileItem.innerHTML = `📄 ${file.name} (${formatFileSize(file.size)})`;
                    fileItem.style.padding = '5px 0';
                    fileList.appendChild(fileItem);
                }
            }

            fileInfo.style.display = 'block';
            uploadBtn.disabled = false;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form submission
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(uploadForm);
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressContainer.style.display = 'block';
            uploadBtn.disabled = true;

            try {
                const response = await fetch('upload.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', result.message);
                    loadStats();
                    uploadForm.reset();
                    fileInfo.style.display = 'none';
                } else {
                    showAlert('error', result.message);
                }
            } catch (error) {
                showAlert('error', 'حدث خطأ أثناء الاستيراد: ' + error.message);
            }

            progressContainer.style.display = 'none';
            uploadBtn.disabled = false;
        });

        function showAlert(type, message) {
            const alertElement = document.getElementById(type === 'success' ? 'successAlert' : 'errorAlert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        async function loadStats() {
            try {
                const response = await fetch('stats.php');
                const stats = await response.json();

                document.getElementById('totalRecords').textContent = stats.total_records || '0';
                document.getElementById('totalFiles').textContent = stats.total_files || '0';
                document.getElementById('lastImport').textContent = stats.last_import || 'لا يوجد';
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
    </script>
</body>
</html>
