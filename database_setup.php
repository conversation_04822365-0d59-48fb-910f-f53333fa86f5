<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد وإصلاح قاعدة البيانات - CDCO</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .center {
            text-align: center;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .section h3 {
            color: #007cba;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ إعداد وإصلاح قاعدة البيانات - CDCO</h1>
        
        <?php
        require_once __DIR__ . '/db.php';

        $database_created = false;
        $table_created = false;
        $column_added = false;
        $errors = [];

        try {
            echo '<div class="status info">🔄 جاري فحص وإعداد قاعدة البيانات...</div>';
            
            // Step 1: Connect without selecting database
            $conn = db_connect(false);
            echo '<div class="status success">✅ تم الاتصال بخادم MySQL بنجاح</div>';

            // Step 2: Create database if not exists
            $dbName = $GLOBALS['DB_NAME'];
            $conn->query("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo '<div class="status success">✅ تم التأكد من وجود قاعدة البيانات: ' . $dbName . '</div>';
            $database_created = true;

            // Step 3: Select database
            $conn->select_db($dbName);
            
            // Step 4: Check if table exists
            $table_check = $conn->query("SHOW TABLES LIKE 'ntakat'");
            $table_exists = $table_check->num_rows > 0;
            
            if (!$table_exists) {
                echo '<div class="status info">📝 إنشاء جدول ntakat...</div>';
                
                // Create table for NTAKAT data
                $sql = <<<SQL
CREATE TABLE `ntakat` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `الكود` VARCHAR(191) NULL,
  `المامورية` VARCHAR(255) NULL,
  `العنوان` TEXT NULL,
  `المحافظة` VARCHAR(191) NULL,
  `النطاق` VARCHAR(191) NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `source_file` VARCHAR(255) NULL,
  PRIMARY KEY (`id`),
  INDEX (`الكود`),
  INDEX (`المحافظة`),
  INDEX (`النطاق`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
                $conn->query($sql);
                echo '<div class="status success">✅ تم إنشاء جدول ntakat بنجاح</div>';
                $table_created = true;
            } else {
                echo '<div class="status success">✅ جدول ntakat موجود بالفعل</div>';
                
                // Step 5: Check if source_file column exists
                $column_check = $conn->query("SHOW COLUMNS FROM ntakat LIKE 'source_file'");
                
                if ($column_check->num_rows == 0) {
                    echo '<div class="status warning">⚠️ عمود source_file مفقود - جاري الإضافة...</div>';
                    $conn->query("ALTER TABLE ntakat ADD COLUMN source_file VARCHAR(255) NULL AFTER النطاق");
                    echo '<div class="status success">✅ تم إضافة عمود source_file بنجاح</div>';
                    $column_added = true;
                } else {
                    echo '<div class="status success">✅ عمود source_file موجود</div>';
                }
            }
            
        } catch (Throwable $e) {
            $errors[] = $e->getMessage();
            echo '<div class="status error">❌ خطأ في إعداد قاعدة البيانات: ' . $e->getMessage() . '</div>';
        }
        ?>
        
        <div class="section">
            <h3>📋 هيكل قاعدة البيانات الحالي</h3>
            <?php
            try {
                $conn = db_connect();
                
                // Show table structure
                $result = $conn->query("DESCRIBE ntakat");
                if ($result && $result->num_rows > 0) {
                    echo '<table>';
                    echo '<thead>';
                    echo '<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>الوصف</th></tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    
                    while ($row = $result->fetch_assoc()) {
                        $description = '';
                        switch($row['Field']) {
                            case 'id': $description = 'المعرف الفريد للسجل'; break;
                            case 'الكود': $description = 'كود النقطة'; break;
                            case 'المامورية': $description = 'اسم المأمورية'; break;
                            case 'العنوان': $description = 'عنوان النقطة'; break;
                            case 'المحافظة': $description = 'اسم المحافظة'; break;
                            case 'النطاق': $description = 'النطاق الجغرافي'; break;
                            case 'created_at': $description = 'تاريخ ووقت الإدراج'; break;
                            case 'source_file': $description = 'اسم الملف المصدر'; break;
                        }
                        
                        echo '<tr>';
                        echo '<td><strong>' . $row['Field'] . '</strong></td>';
                        echo '<td>' . $row['Type'] . '</td>';
                        echo '<td>' . $row['Null'] . '</td>';
                        echo '<td>' . $row['Key'] . '</td>';
                        echo '<td>' . ($row['Default'] ?: '-') . '</td>';
                        echo '<td>' . $description . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody>';
                    echo '</table>';
                    
                    // Show record count
                    $count_result = $conn->query("SELECT COUNT(*) as total FROM ntakat");
                    if ($count_result) {
                        $count = $count_result->fetch_assoc()['total'];
                        echo '<div class="status info">📊 عدد السجلات الحالية: <strong>' . number_format($count) . '</strong> سجل</div>';
                    }
                    
                } else {
                    echo '<div class="status error">❌ لا يمكن عرض هيكل الجدول</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="status error">❌ خطأ في عرض هيكل الجدول: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        
        <?php if (empty($errors)): ?>
            <div class="status success">
                <h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>
                <p>✅ قاعدة البيانات جاهزة للاستخدام</p>
                <p>✅ يمكنك الآن استيراد ملفات CSV من الصفحة الرئيسية</p>
                <p>✅ جميع الأعمدة المطلوبة متوفرة</p>
            </div>
        <?php else: ?>
            <div class="status error">
                <h3>❌ توجد مشاكل في الإعداد</h3>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <p>💡 تأكد من تشغيل XAMPP وأن MySQL يعمل بشكل صحيح</p>
            </div>
        <?php endif; ?>
        
        <div class="center">
            <a href="index.php" class="btn btn-success">🏠 الانتقال إلى الصفحة الرئيسية</a>
            <a href="database_setup.php" class="btn">🔄 إعادة تشغيل الإعداد</a>
            <a href="test.php" class="btn" style="background: #17a2b8;">🧪 اختبار النظام</a>
        </div>
    </div>
</body>
</html>
