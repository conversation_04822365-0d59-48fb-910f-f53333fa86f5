<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الملفات - CDCO</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 40px;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            position: relative;
        }
        .upload-area:hover {
            background: #e8f4fd;
            border-color: #0056b3;
        }
        .upload-area.dragover {
            background: #e8f4fd;
            border-color: #0056b3;
            transform: scale(1.02);
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            pointer-events: auto;
            position: relative;
            z-index: 10;
        }
        .btn:hover {
            background: #0056b3;
        }
        .file-input {
            display: none;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border: 1px solid #ddd;
        }
        .click-counter {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار رفع الملفات</h1>
        <p>هذا اختبار للتأكد من أن زر "اختيار الملفات" يعمل بشكل صحيح ولا يفتح حوار الملفات أكثر من مرة.</p>
        
        <div class="click-counter" id="clickCounter">
            عدد مرات فتح حوار الملفات: <strong>0</strong>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <div style="font-size: 3em; margin-bottom: 15px;">📁</div>
            <h3>اسحب وأفلت ملفات CSV هنا</h3>
            <p>أو انقر لاختيار الملفات</p>
            <input type="file" id="fileInput" class="file-input" multiple accept=".csv">
            <button type="button" class="btn" id="selectFilesBtn">
                اختيار الملفات
            </button>
        </div>
        
        <div class="status" id="status">
            <strong>التعليمات:</strong>
            <ul>
                <li>انقر على زر "اختيار الملفات" - يجب أن يفتح حوار واحد فقط</li>
                <li>انقر على المنطقة الفارغة - يجب أن يفتح حوار واحد فقط</li>
                <li>راقب العداد أعلاه للتأكد من عدم التكرار</li>
            </ul>
        </div>
        
        <div id="fileList"></div>
    </div>

    <script>
        let clickCount = 0;
        
        // Elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const selectFilesBtn = document.getElementById('selectFilesBtn');
        const clickCounter = document.getElementById('clickCounter');
        const fileList = document.getElementById('fileList');
        
        // Update click counter
        function updateClickCounter() {
            clickCount++;
            clickCounter.innerHTML = `عدد مرات فتح حوار الملفات: <strong>${clickCount}</strong>`;
            
            if (clickCount > 1) {
                clickCounter.style.background = '#f8d7da';
                clickCounter.style.color = '#721c24';
                clickCounter.style.border = '1px solid #f5c6cb';
            }
        }
        
        // Monitor file input clicks
        const originalClick = fileInput.click;
        fileInput.click = function() {
            updateClickCounter();
            originalClick.call(this);
        };
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });
        
        // Handle click on upload area (but not on the button)
        uploadArea.addEventListener('click', (e) => {
            // Only trigger if the click is not on the button
            if (e.target !== selectFilesBtn && !selectFilesBtn.contains(e.target)) {
                console.log('Upload area clicked');
                fileInput.click();
            }
        });
        
        // Handle button click separately
        selectFilesBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            console.log('Button clicked');
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            if (files.length === 0) return;
            
            fileList.innerHTML = '<h4>الملفات المحددة:</h4>';
            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.innerHTML = `📄 ${file.name} (${formatFileSize(file.size)})`;
                fileItem.style.padding = '5px 0';
                fileList.appendChild(fileItem);
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
