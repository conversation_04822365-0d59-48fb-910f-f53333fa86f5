<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$response = ['success' => false, 'message' => ''];

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }
    
    $conn = db_connect();
    
    // Clear all data from ntakat table
    $result = $conn->query("DELETE FROM ntakat");
    
    if ($result) {
        // Reset auto increment
        $conn->query("ALTER TABLE ntakat AUTO_INCREMENT = 1");
        
        $response['success'] = true;
        $response['message'] = 'تم حذف جميع البيانات بنجاح';
    } else {
        throw new Exception('فشل في حذف البيانات');
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
