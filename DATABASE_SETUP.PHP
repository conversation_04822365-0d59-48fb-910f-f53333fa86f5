<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - CDCO</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .center {
            text-align: center;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ إعداد قاعدة البيانات - CDCO</h1>
        
        <?php
        require_once __DIR__ . '/db.php';

        // Create database and table if not exists
        try {
            echo '<div class="status info">🔄 جاري إعداد قاعدة البيانات...</div>';
            
            $conn = db_connect(false);

            // Create DB if not exists
            $dbName = $GLOBALS['DB_NAME'];
            $conn->query("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo '<div class="status success">✅ تم إنشاء قاعدة البيانات: ' . $dbName . '</div>';

            // Select DB
            $conn->select_db($dbName);

            // Create table for NTAKAT data (allow duplicates)
            $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `ntakat` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `الكود` VARCHAR(191) NULL,
  `المامورية` VARCHAR(255) NULL,
  `العنوان` TEXT NULL,
  `المحافظة` VARCHAR(191) NULL,
  `النطاق` VARCHAR(191) NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `source_file` VARCHAR(255) NULL,
  PRIMARY KEY (`id`),
  INDEX (`الكود`),
  INDEX (`المحافظة`),
  INDEX (`النطاق`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
            $conn->query($sql);
            echo '<div class="status success">✅ تم إنشاء جدول البيانات: ntakat</div>';
            
            // Check if table exists and show structure
            $result = $conn->query("DESCRIBE ntakat");
            if ($result) {
                echo '<div class="status info">📋 هيكل الجدول:</div>';
                echo '<table>';
                echo '<tr><th>العمود</th><th>النوع</th><th>الوصف</th></tr>';
                while ($row = $result->fetch_assoc()) {
                    $description = '';
                    switch($row['Field']) {
                        case 'id': $description = 'المعرف الفريد'; break;
                        case 'الكود': $description = 'كود النقطة'; break;
                        case 'المامورية': $description = 'اسم المأمورية'; break;
                        case 'العنوان': $description = 'عنوان النقطة'; break;
                        case 'المحافظة': $description = 'اسم المحافظة'; break;
                        case 'النطاق': $description = 'النطاق الجغرافي'; break;
                        case 'created_at': $description = 'تاريخ الإدراج'; break;
                        case 'source_file': $description = 'اسم الملف المصدر'; break;
                    }
                    echo '<tr><td>' . $row['Field'] . '</td><td>' . $row['Type'] . '</td><td>' . $description . '</td></tr>';
                }
                echo '</table>';
            }

            echo '<div class="status success">🎉 تم إعداد قاعدة البيانات بنجاح!</div>';
            echo '<div class="status info">💡 يمكنك الآن استيراد ملفات CSV من الصفحة الرئيسية.</div>';
            
        } catch (Throwable $e) {
            echo '<div class="status error">❌ خطأ في إعداد قاعدة البيانات: ' . $e->getMessage() . '</div>';
            echo '<div class="status info">💡 تأكد من تشغيل XAMPP وأن MySQL يعمل بشكل صحيح.</div>';
        }
        ?>
        
        <div class="center">
            <a href="index.php" class="btn">🏠 الانتقال إلى الصفحة الرئيسية</a>
            <a href="DATABASE_SETUP.PHP" class="btn" style="background: #6c757d;">🔄 إعادة تشغيل الإعداد</a>
        </div>
    </div>
</body>
</html>
