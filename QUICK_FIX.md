# حل سريع لمشكلة عمود source_file

## المشكلة
ظهور خطأ: `Unknown column 'source_file' in 'field list'`

## الحل السريع

### الخيار 1: إصلاح قاعدة البيانات (الأسرع)
1. اذه<PERSON> إلى: `http://localhost/CDCO/fix_database.php`
2. سيتم إضافة العمود المفقود تلقائياً
3. ارجع إلى الصفحة الرئيسية واستخدم النظام

### الخيار 2: إعادة إنشاء قاعدة البيانات
1. اذهب إلى phpMyAdmin: `http://localhost/phpmyadmin`
2. احذف قاعدة البيانات `cdco` إذا كانت موجودة
3. اذهب إلى: `http://localhost/CDCO/DATABASE_SETUP.PHP`
4. سيتم إنشاء قاعدة البيانات من جديد بالهيكل الصحيح

## ما تم إصلاحه
- ✅ ملف `upload.php` يتحقق من وجود عمود `source_file` قبل الاستخدام
- ✅ ملف `view_data.php` يعرض البيانات حتى بدون عمود `source_file`
- ✅ ملف `export.php` يصدر البيانات بالأعمدة المتاحة
- ✅ ملف `fix_database.php` يضيف العمود المفقود

## التحقق من الحل
1. اذهب إلى: `http://localhost/CDCO/test.php`
2. تأكد من ظهور جميع الأعمدة بما في ذلك `source_file`
3. جرب رفع ملف CSV للتأكد من عمل النظام

## ملاحظات
- النظام الآن يعمل حتى لو لم يكن عمود `source_file` موجود
- يُنصح بتشغيل `fix_database.php` لإضافة العمود للاستفادة من ميزة تتبع مصدر الملفات
