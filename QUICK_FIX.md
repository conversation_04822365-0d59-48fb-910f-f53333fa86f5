# حل سريع لمشكلة عمود source_file

## المشكلة
ظهور خطأ: `Unknown column 'source_file' in 'field list'`

## الحل السريع

### الحل الموحد: إعداد وإصلاح قاعدة البيانات
1. اذهب إلى: `http://localhost/CDCO/database_setup.php`
2. سيتم فحص قاعدة البيانات وإصلاح أي مشاكل تلقائياً
3. سيتم إضافة العمود المفقود إذا لم يكن موجود
4. ارجع إلى الصفحة الرئيسية واستخدم النظام

### الخيار البديل: إعادة إنشاء قاعدة البيانات
1. اذهب إلى phpMyAdmin: `http://localhost/phpmyadmin`
2. احذف قاعدة البيانات `cdco` إذا كانت موجودة
3. اذه<PERSON> إلى: `http://localhost/CDCO/database_setup.php`
4. سيتم إنشاء قاعدة البيانات من جديد بالهيكل الصحيح

## ما تم إصلاحه
- ✅ ملف `upload.php` يتحقق من وجود عمود `source_file` قبل الاستخدام
- ✅ ملف `view_data.php` يعرض البيانات حتى بدون عمود `source_file`
- ✅ ملف `export.php` يصدر البيانات بالأعمدة المتاحة
- ✅ ملف `database_setup.php` يقوم بالإعداد والإصلاح في ملف واحد

## التحقق من الحل
1. اذهب إلى: `http://localhost/CDCO/test.php`
2. تأكد من ظهور جميع الأعمدة بما في ذلك `source_file`
3. جرب رفع ملف CSV للتأكد من عمل النظام

## ملاحظات
- النظام الآن يعمل حتى لو لم يكن عمود `source_file` موجود
- يُنصح بتشغيل `database_setup.php` لإضافة العمود للاستفادة من ميزة تتبع مصدر الملفات
