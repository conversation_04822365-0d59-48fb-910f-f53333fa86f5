<?php
require_once 'db.php';

// Set headers for CSV download
$filename = 'ntakat_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

try {
    $conn = db_connect();

    // Check if source_file column exists
    $result_check = $conn->query("SHOW COLUMNS FROM ntakat LIKE 'source_file'");
    $has_source_file = $result_check->num_rows > 0;

    // Create output stream
    $output = fopen('php://output', 'w');

    // Add BOM for UTF-8 to ensure proper display in Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Write CSV header
    $headers = [
        'الرقم',
        'الكود',
        'المأمورية',
        'العنوان',
        'المحافظة',
        'النطاق',
        'تاريخ الإدراج'
    ];

    if ($has_source_file) {
        $headers[] = 'الملف المصدر';
    }

    fputcsv($output, $headers);

    // Get all data
    $sql = "SELECT * FROM ntakat ORDER BY id";
    $result = $conn->query($sql);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $data = [
                $row['id'],
                $row['الكود'],
                $row['المامورية'],
                $row['العنوان'],
                $row['المحافظة'],
                $row['النطاق'],
                $row['created_at']
            ];

            if ($has_source_file) {
                $data[] = $row['source_file'] ?? '';
            }

            fputcsv($output, $data);
        }
    }

    fclose($output);
    
} catch (Exception $e) {
    // If error occurs, show error page instead of download
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head><meta charset='UTF-8'><title>خطأ في التصدير</title></head>";
    echo "<body style='font-family: Arial; padding: 20px; direction: rtl;'>";
    echo "<h2>خطأ في تصدير البيانات</h2>";
    echo "<p>حدث خطأ أثناء تصدير البيانات: " . $e->getMessage() . "</p>";
    echo "<a href='view_data.php'>العودة إلى عرض البيانات</a>";
    echo "</body></html>";
}
?>
