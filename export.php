<?php
require_once 'db.php';

// Set headers for CSV download
$filename = 'ntakat_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

try {
    $conn = db_connect();
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8 to ensure proper display in Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Write CSV header
    fputcsv($output, [
        'الرقم',
        'الكود', 
        'المأمورية',
        'العنوان',
        'المحافظة',
        'النطاق',
        'تاريخ الإدراج',
        'الملف المصدر'
    ]);
    
    // Get all data
    $sql = "SELECT * FROM ntakat ORDER BY id";
    $result = $conn->query($sql);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            fputcsv($output, [
                $row['id'],
                $row['الكود'],
                $row['المامورية'],
                $row['العنوان'],
                $row['المحافظة'],
                $row['النطاق'],
                $row['created_at'],
                $row['source_file']
            ]);
        }
    }
    
    fclose($output);
    
} catch (Exception $e) {
    // If error occurs, show error page instead of download
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head><meta charset='UTF-8'><title>خطأ في التصدير</title></head>";
    echo "<body style='font-family: Arial; padding: 20px; direction: rtl;'>";
    echo "<h2>خطأ في تصدير البيانات</h2>";
    echo "<p>حدث خطأ أثناء تصدير البيانات: " . $e->getMessage() . "</p>";
    echo "<a href='view_data.php'>العودة إلى عرض البيانات</a>";
    echo "</body></html>";
}
?>
