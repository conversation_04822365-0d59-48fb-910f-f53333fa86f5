# تعليمات تشغيل نظام استيراد ملفات CSV - CDCO

## خطوات التشغيل السريع

### 1. تأكد من تشغيل XAMPP
- شغل Apache
- شغل MySQL

### 2. إعد<PERSON> قاعدة البيانات
- اذهب إلى: `http://localhost/CDCO/DATABASE_SETUP.PHP`
- انتظر حتى يتم إنشاء قاعدة البيانات والجداول

### 3. بدء الاستخدام
- اذهب إلى: `http://localhost/CDCO/`
- اختر ملفات CSV للرفع
- انقر "بدء الاستيراد"

## الملفات الرئيسية

| الملف | الوصف |
|-------|-------|
| `index.php` | الصفحة الرئيسية لرفع الملفات |
| `DATABASE_SETUP.PHP` | إعداد قاعدة البيانات |
| `upload.php` | معالج رفع الملفات |
| `view_data.php` | عرض البيانات المستوردة |
| `stats.php` | إحصائيات قاعدة البيانات |
| `export.php` | تصدير البيانات |
| `db.php` | إعدادات قاعدة البيانات |

## اختبار النظام

### اختبار الاتصال
- اذهب إلى: `http://localhost/CDCO/test.php`

### اختبار الاستيراد
- استخدم الملف: `sample_data.csv`

## المميزات المتاحة

✅ **رفع ملفات متعددة**: يمكن رفع عدة ملفات CSV في نفس الوقت

✅ **السحب والإفلات**: اسحب الملفات مباشرة إلى المنطقة المخصصة

✅ **دعم الملفات الكبيرة**: يدعم ملفات حتى 100 ميجابايت

✅ **البحث والتصفية**: بحث في البيانات المستوردة

✅ **التصدير**: تصدير البيانات إلى ملف CSV

✅ **الإحصائيات**: عرض إحصائيات فورية

✅ **دعم اللغة العربية**: واجهة باللغة العربية مع دعم UTF-8

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل MySQL في XAMPP
- تحقق من إعدادات `db.php`

### خطأ في رفع الملفات
- تأكد من أن الملف بصيغة CSV
- تحقق من حجم الملف (أقل من 100 ميجابايت)

### البيانات لا تظهر
- تأكد من وجود البيانات في قاعدة البيانات
- اذهب إلى `view_data.php` للتحقق

## الدعم الفني
- تحقق من ملف `error.log` للأخطاء
- استخدم `test.php` لاختبار النظام
- راجع `README.md` للمزيد من التفاصيل

## المرحلة التالية
بعد التأكد من عمل النظام بشكل صحيح، سيتم إضافة ميزات المقارنة بين الملفات في المرحلة الثانية.
