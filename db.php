<?php
// Database configuration for XAMPP (adjust if needed)
// Default XAMPP MySQL credentials: user 'root' with empty password
$DB_HOST = 'localhost';
$DB_USER = 'root';
$DB_PASS = '';
$DB_NAME = 'cdco';

/**
 * Get a mysqli connection. If $select_db is false, it will not select a database (useful before DB creation).
 */
function db_connect(bool $select_db = true): mysqli {
    global $DB_HOST, $DB_USER, $DB_PASS, $DB_NAME;

    mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
    $mysqli = new mysqli($DB_HOST, $DB_USER, $DB_PASS);
    // Ensure UTF-8 for Arabic content
    if (method_exists($mysqli, 'set_charset')) {
        $mysqli->set_charset('utf8mb4');
    } else {
        $mysqli->query("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
    }

    if ($select_db) {
        $mysqli->select_db($DB_NAME);
    }

    return $mysqli;
}

