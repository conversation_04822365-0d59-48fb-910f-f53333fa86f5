<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شريط التقدم - CDCO</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .progress-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        .progress-header {
            text-align: center;
            margin-bottom: 15px;
        }
        .progress-header h4 {
            color: #007cba;
            margin-bottom: 5px;
        }
        .progress-message {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007cba, #0056b3);
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
            border-radius: 15px;
        }
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                transparent 25%, 
                rgba(255,255,255,0.2) 25%, 
                rgba(255,255,255,0.2) 50%, 
                transparent 50%, 
                transparent 75%, 
                rgba(255,255,255,0.2) 75%);
            background-size: 20px 20px;
            animation: progressStripes 1s linear infinite;
        }
        @keyframes progressStripes {
            0% { background-position: 0 0; }
            100% { background-position: 20px 0; }
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 0.9em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            z-index: 10;
        }
        .progress-details {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85em;
            color: #6c757d;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007cba;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شريط التقدم</h1>
        <p>هذا اختبار لشريط التقدم المحسن لعملية استيراد الملفات.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="startProgressDemo()">🚀 بدء اختبار شريط التقدم</button>
            <button class="btn" onclick="resetProgress()" style="background: #6c757d;">🔄 إعادة تعيين</button>
        </div>
        
        <div class="progress-container" id="progressContainer" style="display: none;">
            <div class="progress-header">
                <h4>🔄 جاري استيراد الملفات</h4>
                <div class="progress-message" id="progressMessage">
                    برجاء الانتظار... جاري تحضير الملفات للاستيراد
                    <span class="spinner"></span>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
                <div class="progress-text" id="progressText">0%</div>
            </div>
            <div class="progress-details" id="progressDetails">
                <span id="currentFile">الملف الحالي: -</span>
                <span id="fileProgress">0 من 0 ملف</span>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>📋 خطوات الاختبار:</h3>
            <ol>
                <li>انقر على "بدء اختبار شريط التقدم"</li>
                <li>راقب شريط التقدم والرسائل</li>
                <li>لاحظ التأثيرات البصرية والرسوم المتحركة</li>
                <li>تحقق من تحديث النسبة المئوية والتفاصيل</li>
            </ol>
        </div>
    </div>

    <script>
        async function startProgressDemo() {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressMessage = document.getElementById('progressMessage');
            const currentFileElement = document.getElementById('currentFile');
            const fileProgress = document.getElementById('fileProgress');
            
            // Show progress container
            progressContainer.style.display = 'block';
            
            // Simulate file processing
            const files = [
                'sample_data.csv',
                '001.csv',
                '002.csv',
                'NTAKAT.csv'
            ];
            
            const totalFiles = files.length;
            
            // Initial setup
            updateProgress(0, 'جاري البدء...', 0, totalFiles);
            await sleep(500);
            
            updateProgress(5, 'جاري فحص الملفات...', 0, totalFiles);
            await sleep(800);
            
            // Process each file
            for (let i = 0; i < totalFiles; i++) {
                const fileName = files[i];
                const baseProgress = 10 + (i * 80 / totalFiles);
                
                // Start processing file
                updateProgress(baseProgress, `جاري معالجة الملف...`, i + 1, totalFiles, fileName);
                await sleep(600);
                
                // Reading file
                updateProgress(baseProgress + 5, `جاري قراءة البيانات من الملف...`, i + 1, totalFiles, fileName);
                await sleep(800);
                
                // Processing data
                updateProgress(baseProgress + 10, `جاري معالجة البيانات...`, i + 1, totalFiles, fileName);
                await sleep(700);
                
                // Saving to database
                updateProgress(baseProgress + 15, `جاري حفظ البيانات في قاعدة البيانات...`, i + 1, totalFiles, fileName);
                await sleep(900);
            }
            
            updateProgress(95, 'جاري إنهاء العملية...', totalFiles, totalFiles);
            await sleep(500);
            
            updateProgress(100, 'تم الانتهاء من الاستيراد بنجاح!', totalFiles, totalFiles);
            
            // Auto hide after completion
            setTimeout(() => {
                progressContainer.style.display = 'none';
            }, 3000);
        }
        
        function updateProgress(percentage, message, currentFile, totalFiles, fileName = '') {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressMessage = document.getElementById('progressMessage');
            const currentFileElement = document.getElementById('currentFile');
            const fileProgress = document.getElementById('fileProgress');
            
            progressFill.style.width = percentage + '%';
            progressText.textContent = Math.round(percentage) + '%';
            
            if (message) {
                if (percentage === 100) {
                    progressMessage.innerHTML = '✅ ' + message;
                } else {
                    progressMessage.innerHTML = message + ' <span class="spinner"></span>';
                }
            }
            
            if (fileName) {
                currentFileElement.textContent = `الملف الحالي: ${fileName}`;
            }
            
            fileProgress.textContent = `${currentFile} من ${totalFiles} ملف`;
        }
        
        function resetProgress() {
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'none';
            
            // Reset all values
            updateProgress(0, 'جاري البدء...', 0, 0);
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>
