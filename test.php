<?php
// Test script to verify database connection and table structure
require_once 'db.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // Test database connection
    $conn = db_connect();
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // Check if table exists
    $result = $conn->query("SHOW TABLES LIKE 'ntakat'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول ntakat موجود</p>";
        
        // Show table structure
        $result = $conn->query("DESCRIBE ntakat");
        echo "<h3>هيكل الجدول:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count records
        $result = $conn->query("SELECT COUNT(*) as count FROM ntakat");
        $row = $result->fetch_assoc();
        echo "<p>عدد السجلات الحالية: <strong>" . $row['count'] . "</strong></p>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول ntakat غير موجود</p>";
        echo "<p><a href='DATABASE_SETUP.PHP'>انقر هنا لإعداد قاعدة البيانات</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    direction: rtl;
    margin: 20px;
}
table {
    width: 100%;
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background-color: #f2f2f2;
}
</style>
