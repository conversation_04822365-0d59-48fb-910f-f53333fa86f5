<?php
// Configuration file for CDCO CSV Import System

// Application settings
define('APP_NAME', 'نظام استيراد ملفات CSV - CDCO');
define('APP_VERSION', '1.0.0');
define('APP_AUTHOR', 'CDCO Development Team');

// File upload settings
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('ALLOWED_EXTENSIONS', ['csv']);
define('UPLOAD_DIR', __DIR__ . '/uploads/');

// Database settings (imported from db.php)
require_once 'db.php';

// CSV processing settings
define('CSV_DELIMITER', ',');
define('CSV_ENCLOSURE', '"');
define('CSV_ESCAPE', '\\');

// Performance settings
define('BATCH_SIZE', 1000); // Number of records to process in each batch
define('MAX_EXECUTION_TIME', 0); // No time limit
define('MEMORY_LIMIT', '1024M'); // 1GB memory limit

// Expected CSV columns
define('CSV_COLUMNS', [
    'الكود',
    'المامورية', 
    'العنوان',
    'المحافظة',
    'النطاق'
]);

// Minimum required columns (for files with only 3 columns)
define('MIN_COLUMNS', 3);

// Application functions
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

function validateCsvFile($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $handle = fopen($file_path, 'r');
    if (!$handle) {
        return false;
    }
    
    // Check if file has at least one line
    $first_line = fgets($handle);
    fclose($handle);
    
    return !empty($first_line);
}

function logError($message, $file = null) {
    $log_message = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($file) {
        $log_message .= ' - File: ' . $file;
    }
    error_log($log_message . PHP_EOL, 3, __DIR__ . '/error.log');
}

function logInfo($message) {
    $log_message = date('Y-m-d H:i:s') . ' - INFO: ' . $message;
    error_log($log_message . PHP_EOL, 3, __DIR__ . '/info.log');
}

// Set PHP configuration for large file processing
ini_set('memory_limit', MEMORY_LIMIT);
ini_set('max_execution_time', MAX_EXECUTION_TIME);
ini_set('upload_max_filesize', formatFileSize(MAX_FILE_SIZE));
ini_set('post_max_size', formatFileSize(MAX_FILE_SIZE));
set_time_limit(0); // Remove time limit completely

// Create upload directory if it doesn't exist
if (!is_dir(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
?>
