<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$response = [
    'total_records' => 0,
    'total_files' => 0,
    'last_import' => 'لا يوجد'
];

try {
    $conn = db_connect();
    
    // Get total records count
    $result = $conn->query("SELECT COUNT(*) as total FROM ntakat");
    if ($result && $row = $result->fetch_assoc()) {
        $response['total_records'] = number_format($row['total']);
    }
    
    // Get unique files count
    $result = $conn->query("SELECT COUNT(DISTINCT source_file) as total_files FROM ntakat WHERE source_file IS NOT NULL");
    if ($result && $row = $result->fetch_assoc()) {
        $response['total_files'] = $row['total_files'];
    }
    
    // Get last import date
    $result = $conn->query("SELECT MAX(created_at) as last_import FROM ntakat");
    if ($result && $row = $result->fetch_assoc() && $row['last_import']) {
        $date = new DateTime($row['last_import']);
        $response['last_import'] = $date->format('Y-m-d H:i');
    }
    
} catch (Exception $e) {
    // Return default values on error
    error_log("Stats error: " . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
