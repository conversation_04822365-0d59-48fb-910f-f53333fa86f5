<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض البيانات - CDCO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .search-box {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            width: 300px;
            max-width: 100%;
        }
        
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }
        
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination a:hover {
            background: #007cba;
            color: white;
        }
        
        .pagination .current {
            background: #007cba;
            color: white;
        }
        
        .stats-bar {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 عرض البيانات المستوردة</h1>
            <p>استعراض وإدارة بيانات النقاط المستوردة</p>
        </div>
        
        <div class="main-card">
            <div class="controls">
                <div>
                    <input type="text" id="searchBox" class="search-box" placeholder="البحث في البيانات...">
                    <button onclick="searchData()" class="btn">🔍 بحث</button>
                </div>
                <div>
                    <a href="index.php" class="btn btn-secondary">🏠 الرئيسية</a>
                    <button onclick="exportData()" class="btn">📥 تصدير</button>
                    <button onclick="clearData()" class="btn btn-danger">🗑️ مسح البيانات</button>
                </div>
            </div>
            
            <?php
            require_once 'db.php';
            
            // Pagination settings
            $records_per_page = 50;
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $offset = ($page - 1) * $records_per_page;
            
            // Search functionality
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $where_clause = '';
            if (!empty($search)) {
                $where_clause = "WHERE الكود LIKE '%$search%' OR المامورية LIKE '%$search%' OR العنوان LIKE '%$search%' OR المحافظة LIKE '%$search%' OR النطاق LIKE '%$search%'";
            }
            
            try {
                $conn = db_connect();

                // Check if source_file column exists
                $result_check = $conn->query("SHOW COLUMNS FROM ntakat LIKE 'source_file'");
                $has_source_file = $result_check->num_rows > 0;

                // Get total count
                $count_sql = "SELECT COUNT(*) as total FROM ntakat $where_clause";
                $count_result = $conn->query($count_sql);
                $total_records = $count_result->fetch_assoc()['total'];
                $total_pages = ceil($total_records / $records_per_page);

                // Get data for current page
                $sql = "SELECT * FROM ntakat $where_clause ORDER BY id DESC LIMIT $records_per_page OFFSET $offset";
                $result = $conn->query($sql);
                
                echo "<div class='stats-bar'>";
                echo "<div>إجمالي السجلات: <strong>" . number_format($total_records) . "</strong></div>";
                echo "<div>الصفحة: <strong>$page</strong> من <strong>$total_pages</strong></div>";
                if (!empty($search)) {
                    echo "<div>البحث عن: <strong>$search</strong></div>";
                }
                echo "</div>";
                
                if ($result && $result->num_rows > 0) {
                    echo "<table class='data-table'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>الرقم</th>";
                    echo "<th>الكود</th>";
                    echo "<th>المأمورية</th>";
                    echo "<th>العنوان</th>";
                    echo "<th>المحافظة</th>";
                    echo "<th>النطاق</th>";
                    echo "<th>تاريخ الإدراج</th>";
                    if ($has_source_file) {
                        echo "<th>الملف المصدر</th>";
                    }
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";

                    while ($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . $row['id'] . "</td>";
                        echo "<td>" . htmlspecialchars($row['الكود']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['المامورية']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['العنوان']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['المحافظة']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['النطاق']) . "</td>";
                        echo "<td>" . date('Y-m-d H:i', strtotime($row['created_at'])) . "</td>";
                        if ($has_source_file) {
                            echo "<td>" . htmlspecialchars($row['source_file'] ?? '') . "</td>";
                        }
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                    
                    // Pagination
                    if ($total_pages > 1) {
                        echo "<div class='pagination'>";
                        
                        if ($page > 1) {
                            echo "<a href='?page=" . ($page - 1) . ($search ? "&search=" . urlencode($search) : "") . "'>السابق</a>";
                        }
                        
                        for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
                            if ($i == $page) {
                                echo "<span class='current'>$i</span>";
                            } else {
                                echo "<a href='?page=$i" . ($search ? "&search=" . urlencode($search) : "") . "'>$i</a>";
                            }
                        }
                        
                        if ($page < $total_pages) {
                            echo "<a href='?page=" . ($page + 1) . ($search ? "&search=" . urlencode($search) : "") . "'>التالي</a>";
                        }
                        
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='no-data'>";
                    echo "<h3>لا توجد بيانات للعرض</h3>";
                    echo "<p>لم يتم العثور على أي سجلات في قاعدة البيانات</p>";
                    echo "<a href='index.php' class='btn'>استيراد ملفات CSV</a>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='no-data'>";
                echo "<h3>خطأ في قاعدة البيانات</h3>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
            ?>
        </div>
    </div>

    <script>
        function searchData() {
            const searchTerm = document.getElementById('searchBox').value;
            const url = new URL(window.location);
            if (searchTerm.trim()) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }
            url.searchParams.delete('page'); // Reset to first page
            window.location = url;
        }
        
        // Allow search on Enter key
        document.getElementById('searchBox').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchData();
            }
        });
        
        // Set search box value from URL
        const urlParams = new URLSearchParams(window.location.search);
        const searchParam = urlParams.get('search');
        if (searchParam) {
            document.getElementById('searchBox').value = searchParam;
        }
        
        function exportData() {
            window.open('export.php', '_blank');
        }
        
        function clearData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                fetch('clear_data.php', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف البيانات بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                });
            }
        }
    </script>
</body>
</html>
