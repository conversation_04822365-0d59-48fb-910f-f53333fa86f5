<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

// Set memory limit and execution time for large files
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);

$response = ['success' => false, 'message' => '', 'imported_count' => 0];

try {
    // Check if files were uploaded
    if (!isset($_FILES['csv_files']) || empty($_FILES['csv_files']['name'][0])) {
        throw new Exception('لم يتم اختيار أي ملفات للرفع');
    }
    
    $conn = db_connect();
    $total_imported = 0;
    $processed_files = 0;
    
    // Process each uploaded file
    for ($i = 0; $i < count($_FILES['csv_files']['name']); $i++) {
        $file_name = $_FILES['csv_files']['name'][$i];
        $file_tmp = $_FILES['csv_files']['tmp_name'][$i];
        $file_error = $_FILES['csv_files']['error'][$i];
        
        // Skip if file has error
        if ($file_error !== UPLOAD_ERR_OK) {
            continue;
        }
        
        // Validate file extension
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if ($file_ext !== 'csv') {
            continue;
        }
        
        // Process CSV file
        $file_imported = processCsvFile($conn, $file_tmp, $file_name);
        $total_imported += $file_imported;
        $processed_files++;
    }
    
    if ($processed_files === 0) {
        throw new Exception('لم يتم العثور على ملفات CSV صالحة للمعالجة');
    }
    
    $response['success'] = true;
    $response['message'] = "تم استيراد {$total_imported} سجل من {$processed_files} ملف بنجاح";
    $response['imported_count'] = $total_imported;
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
} catch (Error $e) {
    $response['message'] = 'خطأ في النظام: ' . $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

function processCsvFile($conn, $file_path, $file_name) {
    $imported_count = 0;
    
    // Open CSV file
    if (($handle = fopen($file_path, "r")) !== FALSE) {
        // Skip header row
        $header = fgetcsv($handle, 0, ",");
        
        // Prepare insert statement
        $stmt = $conn->prepare("INSERT INTO ntakat (الكود, المامورية, العنوان, المحافظة, النطاق, source_file) VALUES (?, ?, ?, ?, ?, ?)");

        // Process each row
        while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {
            // Skip empty rows
            if (empty(array_filter($data))) {
                continue;
            }

            // Ensure we have at least 5 columns, pad with empty strings if needed
            $data = array_pad($data, 5, '');

            // Clean and prepare data
            $code = trim($data[0] ?? '');
            $mamoria = trim($data[1] ?? '');
            $address = trim($data[2] ?? '');
            $governorate = trim($data[3] ?? ''); // May be empty for 3-column files
            $domain = trim($data[4] ?? '');      // May be empty for 3-column files

            // Insert data (allow duplicates as requested)
            try {
                $stmt->bind_param("ssssss", $code, $mamoria, $address, $governorate, $domain, $file_name);
                $stmt->execute();
                $imported_count++;
            } catch (Exception $e) {
                // Log error but continue processing
                error_log("Error inserting row: " . $e->getMessage());
            }
        }
        
        fclose($handle);
        $stmt->close();
    }
    
    return $imported_count;
}
?>
