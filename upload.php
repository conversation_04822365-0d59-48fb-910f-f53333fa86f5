<?php
header('Content-Type: application/json; charset=utf-8');

// Load performance configuration first
require_once 'performance_config.php';
require_once 'db.php';

// Additional optimizations for this specific script
ini_set('memory_limit', '2048M'); // 2GB for very large files
ini_set('max_execution_time', 0); // No time limit
set_time_limit(0); // Remove time limit
ignore_user_abort(true); // Continue even if user closes browser

$response = [
    'success' => false,
    'message' => '',
    'imported_count' => 0,
    'processed_files' => 0,
    'total_files' => 0,
    'details' => []
];

try {
    // Log initial memory usage
    error_log("Upload started - Initial memory: " . formatBytes(memory_get_usage(true)));

    // Check if files were uploaded
    if (!isset($_FILES['csv_files']) || empty($_FILES['csv_files']['name'][0])) {
        throw new Exception('لم يتم اختيار أي ملفات للرفع');
    }
    
    $conn = db_connect();
    $total_imported = 0;
    $processed_files = 0;
    $total_files = count($_FILES['csv_files']['name']);

    $response['total_files'] = $total_files;

    // Process each uploaded file
    for ($i = 0; $i < $total_files; $i++) {
        $file_name = $_FILES['csv_files']['name'][$i];
        $file_tmp = $_FILES['csv_files']['tmp_name'][$i];
        $file_error = $_FILES['csv_files']['error'][$i];

        // Skip if file has error
        if ($file_error !== UPLOAD_ERR_OK) {
            $response['details'][] = [
                'file' => $file_name,
                'status' => 'error',
                'message' => 'خطأ في رفع الملف: ' . getUploadErrorMessage($file_error),
                'imported' => 0
            ];
            continue;
        }

        // Validate file extension
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if ($file_ext !== 'csv') {
            $response['details'][] = [
                'file' => $file_name,
                'status' => 'skipped',
                'message' => 'تم تجاهل الملف - ليس ملف CSV',
                'imported' => 0
            ];
            continue;
        }

        // Process CSV file
        $start_time = microtime(true);
        $file_imported = processCsvFile($conn, $file_tmp, $file_name);
        $end_time = microtime(true);
        $processing_time = round($end_time - $start_time, 2);

        $total_imported += $file_imported;
        $processed_files++;

        $memory_usage = getMemoryUsage();

        $response['details'][] = [
            'file' => $file_name,
            'status' => 'success',
            'message' => "تم استيراد {$file_imported} سجل في {$processing_time} ثانية",
            'imported' => $file_imported,
            'processing_time' => $processing_time,
            'memory_used' => $memory_usage['current_formatted'],
            'memory_peak' => $memory_usage['peak_formatted']
        ];

        // Log memory usage after each file
        error_log("File {$file_name} processed - Memory: {$memory_usage['current_formatted']}, Peak: {$memory_usage['peak_formatted']}");
    }
    
    if ($processed_files === 0) {
        throw new Exception('لم يتم العثور على ملفات CSV صالحة للمعالجة');
    }
    
    $response['success'] = true;
    $response['message'] = "تم استيراد {$total_imported} سجل من {$processed_files} ملف بنجاح";
    $response['imported_count'] = $total_imported;
    $response['processed_files'] = $processed_files;

    // Add summary
    if ($processed_files < $total_files) {
        $skipped = $total_files - $processed_files;
        $response['message'] .= " (تم تجاهل {$skipped} ملف)";
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
} catch (Error $e) {
    $response['message'] = 'خطأ في النظام: ' . $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

function processCsvFile($conn, $file_path, $file_name) {
    $imported_count = 0;

    // Open CSV file
    if (($handle = fopen($file_path, "r")) !== FALSE) {
        // Skip header row
        $header = fgetcsv($handle, 0, ",");

        // Check if source_file column exists
        $result = $conn->query("SHOW COLUMNS FROM ntakat LIKE 'source_file'");
        $has_source_file = $result->num_rows > 0;

        // Prepare insert statement based on available columns
        if ($has_source_file) {
            $stmt = $conn->prepare("INSERT INTO ntakat (الكود, المامورية, العنوان, المحافظة, النطاق, source_file) VALUES (?, ?, ?, ?, ?, ?)");
        } else {
            $stmt = $conn->prepare("INSERT INTO ntakat (الكود, المامورية, العنوان, المحافظة, النطاق) VALUES (?, ?, ?, ?, ?)");
        }

        // Process each row
        while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {
            // Skip empty rows
            if (empty(array_filter($data))) {
                continue;
            }

            // Ensure we have at least 5 columns, pad with empty strings if needed
            $data = array_pad($data, 5, '');

            // Clean and prepare data
            $code = trim($data[0] ?? '');
            $mamoria = trim($data[1] ?? '');
            $address = trim($data[2] ?? '');
            $governorate = trim($data[3] ?? ''); // May be empty for 3-column files
            $domain = trim($data[4] ?? '');      // May be empty for 3-column files

            // Insert data (allow duplicates as requested)
            try {
                if ($has_source_file) {
                    $stmt->bind_param("ssssss", $code, $mamoria, $address, $governorate, $domain, $file_name);
                } else {
                    $stmt->bind_param("sssss", $code, $mamoria, $address, $governorate, $domain);
                }
                $stmt->execute();
                $imported_count++;
            } catch (Exception $e) {
                // Log error but continue processing
                error_log("Error inserting row: " . $e->getMessage());
            }
        }

        fclose($handle);
        $stmt->close();
    }

    return $imported_count;
}

function getUploadErrorMessage($error_code) {
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return 'الملف أكبر من الحد المسموح في إعدادات PHP';
        case UPLOAD_ERR_FORM_SIZE:
            return 'الملف أكبر من الحد المسموح في النموذج';
        case UPLOAD_ERR_PARTIAL:
            return 'تم رفع جزء من الملف فقط';
        case UPLOAD_ERR_NO_FILE:
            return 'لم يتم رفع أي ملف';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'مجلد الملفات المؤقتة غير موجود';
        case UPLOAD_ERR_CANT_WRITE:
            return 'فشل في كتابة الملف على القرص';
        case UPLOAD_ERR_EXTENSION:
            return 'امتداد PHP أوقف رفع الملف';
        default:
            return 'خطأ غير معروف في رفع الملف';
    }
}
?>
