<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات الأداء - CDCO</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
            text-align: center;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .status-card h3 {
            color: #007cba;
            margin-bottom: 15px;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .setting-item:last-child {
            border-bottom: none;
        }
        .setting-name {
            font-weight: bold;
            color: #495057;
        }
        .setting-value {
            color: #007cba;
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 4px;
        }
        .status-good {
            color: #28a745;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .status-bad {
            color: #dc3545;
            font-weight: bold;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        .recommendations ul {
            color: #856404;
            margin: 0;
            padding-right: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار إعدادات الأداء العالي</h1>
        
        <?php
        // Load performance configuration
        require_once 'performance_config.php';
        
        // Get current limits
        $limits = displayCurrentLimits();
        $memory_usage = getMemoryUsage();
        
        // Check if settings are optimal
        function checkSetting($value, $recommended, $type = 'exact') {
            if ($type === 'memory') {
                $valueBytes = parseMemoryLimit($value);
                $recommendedBytes = parseMemoryLimit($recommended);
                if ($valueBytes >= $recommendedBytes || $valueBytes == -1) {
                    return 'good';
                } else {
                    return 'bad';
                }
            } elseif ($type === 'time') {
                if ($value == 0 || $value >= $recommended) {
                    return 'good';
                } else {
                    return 'warning';
                }
            } else {
                return $value >= $recommended ? 'good' : 'bad';
            }
        }
        ?>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>⏱️ إعدادات الوقت</h3>
                <div class="setting-item">
                    <span class="setting-name">وقت التنفيذ الأقصى</span>
                    <span class="setting-value <?php echo checkSetting($limits['max_execution_time'], 0, 'time') === 'good' ? 'status-good' : 'status-warning'; ?>">
                        <?php echo $limits['max_execution_time'] == 0 ? 'بلا حدود' : $limits['max_execution_time'] . ' ثانية'; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">وقت الإدخال الأقصى</span>
                    <span class="setting-value <?php echo checkSetting($limits['max_input_time'], 0, 'time') === 'good' ? 'status-good' : 'status-warning'; ?>">
                        <?php echo $limits['max_input_time'] == 0 ? 'بلا حدود' : $limits['max_input_time'] . ' ثانية'; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">مهلة الاتصال</span>
                    <span class="setting-value">
                        <?php echo $limits['default_socket_timeout']; ?> ثانية
                    </span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>💾 إعدادات الذاكرة</h3>
                <div class="setting-item">
                    <span class="setting-name">حد الذاكرة</span>
                    <span class="setting-value <?php echo checkSetting($limits['memory_limit'], '1024M', 'memory') === 'good' ? 'status-good' : 'status-bad'; ?>">
                        <?php echo $limits['memory_limit']; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">الذاكرة المستخدمة حالياً</span>
                    <span class="setting-value">
                        <?php echo $memory_usage['current_formatted']; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">أعلى استخدام للذاكرة</span>
                    <span class="setting-value">
                        <?php echo $memory_usage['peak_formatted']; ?>
                    </span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>📁 إعدادات الرفع</h3>
                <div class="setting-item">
                    <span class="setting-name">حجم الملف الأقصى</span>
                    <span class="setting-value <?php echo checkSetting($limits['upload_max_filesize'], '100M', 'memory') === 'good' ? 'status-good' : 'status-bad'; ?>">
                        <?php echo $limits['upload_max_filesize']; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">حجم POST الأقصى</span>
                    <span class="setting-value <?php echo checkSetting($limits['post_max_size'], '100M', 'memory') === 'good' ? 'status-good' : 'status-bad'; ?>">
                        <?php echo $limits['post_max_size']; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">عدد المتغيرات الأقصى</span>
                    <span class="setting-value <?php echo checkSetting($limits['max_input_vars'], 10000) === 'good' ? 'status-good' : 'status-bad'; ?>">
                        <?php echo number_format($limits['max_input_vars']); ?>
                    </span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>🔧 معلومات النظام</h3>
                <div class="setting-item">
                    <span class="setting-name">إصدار PHP</span>
                    <span class="setting-value">
                        <?php echo PHP_VERSION; ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">نوع الخادم</span>
                    <span class="setting-value">
                        <?php echo php_sapi_name(); ?>
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-name">نظام التشغيل</span>
                    <span class="setting-value">
                        <?php echo PHP_OS; ?>
                    </span>
                </div>
            </div>
        </div>
        
        <?php
        // Check for potential issues
        $issues = [];
        $recommendations = [];
        
        if (parseMemoryLimit($limits['memory_limit']) < parseMemoryLimit('1024M') && $limits['memory_limit'] != -1) {
            $issues[] = "حد الذاكرة أقل من 1GB";
            $recommendations[] = "زيادة memory_limit إلى 1024M أو أكثر";
        }
        
        if ($limits['max_execution_time'] != 0 && $limits['max_execution_time'] < 600) {
            $issues[] = "وقت التنفيذ محدود";
            $recommendations[] = "تعيين max_execution_time إلى 0 (بلا حدود)";
        }
        
        if (parseMemoryLimit($limits['upload_max_filesize']) < parseMemoryLimit('100M')) {
            $issues[] = "حجم الملف المسموح صغير";
            $recommendations[] = "زيادة upload_max_filesize إلى 100M أو أكثر";
        }
        
        if ($limits['max_input_vars'] < 10000) {
            $issues[] = "عدد المتغيرات محدود";
            $recommendations[] = "زيادة max_input_vars إلى 10000 أو أكثر";
        }
        
        if (!empty($issues)):
        ?>
        <div class="recommendations">
            <h4>⚠️ توصيات للتحسين:</h4>
            <ul>
                <?php foreach ($recommendations as $recommendation): ?>
                    <li><?php echo $recommendation; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php else: ?>
        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center;">
            <h4 style="color: #155724; margin: 0;">✅ جميع الإعدادات محسنة للأداء العالي!</h4>
            <p style="color: #155724; margin: 10px 0 0 0;">النظام جاهز لمعالجة الملفات الكبيرة بكفاءة عالية.</p>
        </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="index.php" class="btn">🏠 العودة إلى الصفحة الرئيسية</a>
            <a href="test_performance.php" class="btn" style="background: #6c757d;">🔄 إعادة الاختبار</a>
            <button onclick="testLargeOperation()" class="btn" style="background: #28a745;">🧪 اختبار عملية كبيرة</button>
        </div>
    </div>

    <script>
        function testLargeOperation() {
            if (confirm('هل تريد اختبار عملية تستغرق وقتاً طويلاً؟ (قد تستغرق عدة ثوانٍ)')) {
                window.location.href = 'test_performance.php?test_operation=1';
            }
        }
    </script>
    
    <?php
    // Test large operation if requested
    if (isset($_GET['test_operation'])) {
        echo "<script>alert('بدء اختبار العملية الطويلة...');</script>";
        
        $start_time = microtime(true);
        $start_memory = memory_get_usage(true);
        
        // Simulate large operation
        for ($i = 0; $i < 1000000; $i++) {
            $data[] = "Test data row " . $i;
            if ($i % 100000 == 0) {
                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }
        
        $end_time = microtime(true);
        $end_memory = memory_get_usage(true);
        
        $execution_time = round($end_time - $start_time, 2);
        $memory_used = formatBytes($end_memory - $start_memory);
        
        echo "<script>alert('اكتمل الاختبار!\\nالوقت: {$execution_time} ثانية\\nالذاكرة المستخدمة: {$memory_used}');</script>";
    }
    ?>
</body>
</html>
