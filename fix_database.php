<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - CDCO</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .center {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح قاعدة البيانات - CDCO</h1>
        
        <?php
        require_once __DIR__ . '/db.php';

        try {
            echo '<div class="status info">🔄 جاري فحص وإصلاح قاعدة البيانات...</div>';
            
            $conn = db_connect();
            
            // Check if source_file column exists
            $result = $conn->query("SHOW COLUMNS FROM ntakat LIKE 'source_file'");
            
            if ($result->num_rows == 0) {
                // Column doesn't exist, add it
                echo '<div class="status info">📝 إضافة عمود source_file المفقود...</div>';
                $conn->query("ALTER TABLE ntakat ADD COLUMN source_file VARCHAR(255) NULL AFTER النطاق");
                echo '<div class="status success">✅ تم إضافة عمود source_file بنجاح</div>';
            } else {
                echo '<div class="status success">✅ عمود source_file موجود بالفعل</div>';
            }
            
            // Show current table structure
            $result = $conn->query("DESCRIBE ntakat");
            if ($result) {
                echo '<div class="status info">📋 هيكل الجدول الحالي:</div>';
                echo '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';
                echo '<tr style="background: #f8f9fa;"><th style="border: 1px solid #ddd; padding: 8px;">العمود</th><th style="border: 1px solid #ddd; padding: 8px;">النوع</th><th style="border: 1px solid #ddd; padding: 8px;">Null</th></tr>';
                while ($row = $result->fetch_assoc()) {
                    echo '<tr>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $row['Field'] . '</td>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $row['Type'] . '</td>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $row['Null'] . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '<div class="status success">🎉 تم إصلاح قاعدة البيانات بنجاح!</div>';
            echo '<div class="status info">💡 يمكنك الآن استيراد ملفات CSV بدون مشاكل.</div>';
            
        } catch (Throwable $e) {
            echo '<div class="status error">❌ خطأ في إصلاح قاعدة البيانات: ' . $e->getMessage() . '</div>';
            echo '<div class="status info">💡 تأكد من تشغيل XAMPP وأن MySQL يعمل بشكل صحيح.</div>';
        }
        ?>
        
        <div class="center">
            <a href="index.php" class="btn">🏠 الانتقال إلى الصفحة الرئيسية</a>
            <a href="DATABASE_SETUP.PHP" class="btn" style="background: #6c757d;">🔄 إعادة إعداد قاعدة البيانات</a>
        </div>
    </div>
</body>
</html>
