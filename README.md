# نظام استيراد ملفات CSV - CDCO

## الوصف
تطبيق ويب لاستيراد ودمج ملفات CSV في قاعدة بيانات MySQL. يدعم التطبيق استيراد ملفات متعددة ويسمح بالتكرار في البيانات.

## المتطلبات
- XAMPP (Apache + MySQL + PHP)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث

## هيكل البيانات
يحتوي كل ملف CSV على 5 أعمدة:
1. **الكود** - كود النقطة
2. **المأمورية** - اسم المأمورية
3. **العنوان** - عنوان النقطة
4. **المحافظة** - اسم المحافظة
5. **النطاق** - النطاق الجغرافي

## الملفات المطلوبة
- `index.php` - الصفحة الرئيسية لرفع الملفات
- `upload.php` - معالج رفع وتحليل ملفات CSV
- `stats.php` - عرض إحصائيات قاعدة البيانات
- `db.php` - إعدادات الاتصال بقاعدة البيانات
- `DATABASE_SETUP.PHP` - إعداد قاعدة البيانات والجداول

## طريقة التشغيل

### 1. إعداد XAMPP
1. تأكد من تشغيل Apache و MySQL في XAMPP
2. ضع ملفات التطبيق في مجلد `htdocs/CDCO`

### 2. إعداد قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost/CDCO/DATABASE_SETUP.PHP`
2. سيتم إنشاء قاعدة البيانات والجداول تلقائياً

### 3. استخدام التطبيق
1. اذهب إلى: `http://localhost/CDCO/`
2. اختر ملفات CSV للرفع
3. انقر على "بدء الاستيراد"
4. راقب الإحصائيات في الصفحة الرئيسية

## المميزات
- ✅ دعم رفع ملفات متعددة
- ✅ واجهة سهلة الاستخدام باللغة العربية
- ✅ السحب والإفلات للملفات
- ✅ عرض إحصائيات فورية
- ✅ دعم الملفات الكبيرة
- ✅ السماح بالتكرار في البيانات
- ✅ تتبع مصدر كل سجل

## هيكل قاعدة البيانات

### جدول `ntakat`
```sql
CREATE TABLE `ntakat` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `الكود` VARCHAR(191) NULL,
  `المامورية` VARCHAR(255) NULL,
  `العنوان` TEXT NULL,
  `المحافظة` VARCHAR(191) NULL,
  `النطاق` VARCHAR(191) NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `source_file` VARCHAR(255) NULL,
  PRIMARY KEY (`id`),
  INDEX (`الكود`),
  INDEX (`المحافظة`),
  INDEX (`النطاق`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## الإعدادات
يمكن تعديل إعدادات قاعدة البيانات في ملف `db.php`:
```php
$DB_HOST = 'localhost';
$DB_USER = 'root';
$DB_PASS = '';
$DB_NAME = 'cdco';
```

## استكشاف الأخطاء
- تأكد من تشغيل XAMPP
- تأكد من صحة إعدادات قاعدة البيانات
- تحقق من صيغة ملفات CSV (UTF-8)
- تأكد من وجود الأعمدة الخمسة في كل ملف

## المرحلة التالية
سيتم إضافة ميزات المقارنة بين الملفات في المرحلة الثانية.
